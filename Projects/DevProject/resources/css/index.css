* {
  font-family: Helvetica, Arial, sans-serif;
}

header {
  background-color: #333333;
  color: whitesmoke;
  padding: 10px 0;
  position: fixed;
  width: 100%;
  z-index: 10;
}

nav li {
  color: #fff;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 16px;
  display: inline-block;
  width: 80px;
}

h1 {
  color: whitesmoke;
}


p, 
li {
  font-size: 18px;
}


a {
  text-decoration: none;
}

button {
  background-color: whitesmoke;
  color: whitesmoke;
  padding: 10px 10px;
  border-radius: 5px;
  font-size: 18px;
  margin-top: 20px;
}

#intro {
  background-image: url("../images/2019-02-24_14-29-36_034.jpeg");
  background-size: cover;
  background-position: center;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  text-align: center;
  position: relative;
  top: 60px;
  padding: 10px 50px;
}

#intro li {
    text-align: center;
    list-style-position: inside;
}

.container img {
  width: 400px;
  height: 400px;
  border-radius: 50%;
}