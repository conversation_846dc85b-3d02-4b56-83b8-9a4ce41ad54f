table {
  table-layout: fixed;
  width: 80%;
  min-width: 1000px;
  margin: 0 auto;
  border-collapse: collapse;
  border: 1px solid #999;
  border-top: 1px solid #999;
  border-bottom: 1px solid #999;
}

th,
td {
  padding: 0.6em;
  text-wrap: wrap;
  text-align: center;
}

td code {
  font-family: 'SF Mono Spaced', sans-serif;
  font-size: 16px;
}

tbody tr:nth-child(odd) {
  background-color: #eee;
}

caption {
  caption-side: center;
  text-align: center;
  font-weight: bold;
  font-family: 'SF Mono Spaced', sans-serif;
  font-size: 24px;
  margin-bottom: 0.6em;
}
