<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>HTML & CSS Cheatsheet</title>
        <link rel="stylesheet" href="styles.css">
    </head>
    <body>
        <!-- Header -->
         <header>
            <h1>HTML Tags</h1>
        </header>
        <table class="standard-table">
            <caption>Inline elements: usage and examples</caption>
            <thead>
                <tr>
                    <th scope="col">Usage</th>
                    <th scope="col">Element</th>
                    <th scope="col">Example</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>A link</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/a">
                            <code>&lt;a&gt;</code>
                        </a>
                    </td>
                    <td id="a-example">
                        <code>&lt;a href="https://example.org"&gt; A link to example.org&lt;/a&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>An image</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/img">
                            <code>&lt;img&gt;</code>
                        </a>
                    </td>
                    <td id="img-example">
                        <code>&lt;img src="beast.png" width="50" /&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>An inline container</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/span">
                            <code>&lt;span&gt;</code>
                        </a>
                    </td>
                    <td id="span-example">
                        <code>Used to group elements: for example, to &lt;span style="color:blue"&gt;style them&lt;/span&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>Emphasize text</td>
                    <td><a href="/en-US/docs/Web/HTML/Reference/Elements/em"><code>&lt;em&gt;</code></a></td>
                    <td id="em-example">
                        <code>&lt;em&gt;I'm posh&lt;/em&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>Italic text</td>
                    <td><a href="/en-US/docs/Web/HTML/Reference/Elements/i"><code>&lt;i&gt;</code></a></td>
                    <td id="i-example">
                        <code>Mark a phrase in &lt;i&gt;italics&lt;/i&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>Bold text</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/b">
                        <code>&lt;b&gt;</code></a></td>
                    <td id="b-example">
                        <code>Bold &lt;b&gt;a word or phrase&lt;/b&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>Important text</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/strong">
                            <code>&lt;strong&gt;</code>
                        </a>
                    </td>
                    <td id="strong-example">
                        <code>&lt;strong&gt;I'm important!&lt;/strong&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Highlight text</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/mark">
                            <code>&lt;mark&gt;</code>
                        </a>
                    </td>
                    <td id="mark-example">
                        <code>&lt;mark&gt;Notice me!&lt;/mark&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Strikethrough text</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/s">
                            <code>&lt;s&gt;</code>
                        </a>
                    </td>
                    <td id="s-example">
                        <code>&lt;s&gt;I'm irrelevant.&lt;/s&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Subscript</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/sub">
                            <code>&lt;sub&gt;</code>
                        </a>
                    </td>
                    <td id="sub-example">
                        <code>H&lt;sub&gt;2&lt;/sub&gt;O</code>
                    </td>
                </tr>
                <tr>
                    <td>Small text</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/small">
                            <code>&lt;small&gt;</code
                        </a>
                    </td>
                    <td id="small-example">
                        <code>Used to represent the &lt;small&gt;small print &lt;/small&gt;of a document.</code>
                    </td>
                </tr>
                <tr>
                    <td>Address</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/address">
                            <code>&lt;address&gt;</code
                        </a>
                    </td>
                    <td id="address-example">
                        <code>&lt;address&gt;Main street 67&lt;/address&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Textual citation</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/cite">
                            <code>&lt;cite&gt;</code>
                        </a>
                    </td>
                    <td>
                        <code>For more monsters, see &lt;cite&gt;The Monster Book of Monsters&lt;/cite&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>Superscript</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/sup">
                            <code>&lt;sup&gt;</code>
                        </a>
                    </td>
                    <td id="sup-example">
                        <code>x&lt;sup&gt;2&lt;/sup&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Inline quotation</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/q">
                            <code>&lt;q&gt;</code>
                        </a>
                    </td>
                    <td id="q-example">
                        <code>&lt;q&gt;Me?&lt;/q&gt;, she said.</code>
                    </td>
                </tr>
                <tr>
                    <td>A line break</td>
                    <td><a href="/en-US/docs/Web/HTML/Reference/Elements/br"><code>&lt;br&gt;</code></a></td>
                    <td id="br-example">
                        <code>Line 1&lt;br&gt;Line 2</code>
                    </td>
                </tr>
                <tr>
                    <td>A possible line break</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/wbr">
                            <code>&lt;wbr&gt;</code>
                        </a>
                    </td>
                    <td id="wbr-example">
                        <code>&lt;div style="width: 200px"&gt; Llanfair&lt;wbr&gt; pwllgwyngyll&lt;wbr&gt; gogerychwyrndrobwllllantysiliogogogoch. &lt;/div&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Date</td>
                    <td><a href="/en-US/docs/Web/HTML/Reference/Elements/time"><code>&lt;time&gt;</code></a></td>
                    <td id="time-example">
                        <code>Used to format the date. For example: &lt;time datetime="2020-05-24"&gt; published on 23-05-2020&lt;/time&gt;.</code>
                    </td>
                </tr>
                <tr>
                    <td>Code format</td>
                    <td><a href="/en-US/docs/Web/HTML/Reference/Elements/code"><code>&lt;code&gt;</code></a></td>
                    <td id="code-example">
                        <div class="code-example"><code>This text is in normal format, but &lt;code&gt;this text is in code format&lt;/code&gt;.</code></div>
                    </td>
                </tr>
                <tr>
                    <td>Audio</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/audio">
                            <code>&lt;audio&gt;</code>
                        </a>
                    </td>
                    <td id="audio-example">
                        <code>&lt;audio controls&gt; &lt;source src="/shared-assets/audio/t-rex-roar.mp3" type="audio/mpeg"&gt; &lt;/audio&gt;</code>
                    </td>
                </tr>
                <tr>
                    <td>Video</td>
                    <td>
                        <a href="/en-US/docs/Web/HTML/Reference/Elements/video">
                            <code>&lt;video&gt;</code>
                        </a>
                    </td>
                    <td id="video-example">
                        <code>&lt;video controls width="250" src="/shared-assets/videos/flower.webm" &gt; &lt;a href="/shared-assets/videos/flower.webm"&gt;Download WebM video&lt;/a&gt; &lt;/video&gt;</code>
                    </td>
                </tr>
            </tbody>
        </table>
    </body>
</html>