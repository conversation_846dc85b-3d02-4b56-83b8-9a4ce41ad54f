html, body {
  margin: 0;
  padding: 0;
}

header {
  background-color: #333333;
  position: fixed;
  width: 100%;
  z-index: 10;
}

nav {
  margin: 0;
  padding: 20px 0;
}

nav li {
  color: #fff;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 12px;
  display: inline-block;
  width: 80px;
}

main {
  text-align: center;
  position: relative;
  top: 90px;
}

main h1 {
  color: #333;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 70px;
  margin-top: 0px;
  padding-top: 80px;
  margin-bottom: 80px;
  text-transform: uppercase;
}

footer {
  background-color: #333;
  color: #fff;
  padding: 30px 0;
  position: relative;
  top: 40px;
/*  bottom: 0;*/
  width: 100%;
}

footer p {
  font-family: 'Raleway', sans-serif;
  text-transform: uppercase;
  font-size: 11px;
}

.container {
  max-width: 940px;
  margin: 0 auto;
  padding: 0 10px;
  text-align: center;
}

.jumbotron {
  height: 800px;
  background-image: url("https://content.codecademy.com/projects/broadway/bg.jpg");
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.btn-main {
  background-color: #333;
  color: #fff;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 18px;
  letter-spacing: 1.3px;
  padding: 16px 40px;
  text-decoration: none;
  text-transform: uppercase;
}

.btn-default {
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 10px;
  letter-spacing: 1.3px;
  padding: 10px 20px;
  text-decoration: none;
  text-transform: uppercase;  
  margin-bottom: 20px;      
}

/*.supporting {*/
/*  padding-top: 80px;*/
/*  padding-bottom: 100px;*/
/*}*/

.supporting {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.supporting .col {
  font-family: 'Raleway', sans-serif;
  text-align: center;
  display: inline-block;
  top: 140px;
  width: 200px;
  height: 200px;
}

.supporting img {
  height: 32px;
  position: relative;
  top: 120px;
  display: inline-block;
  width: 150px;
}

.supporting h2 {
  font-weight: 600;
  font-size: 23px;
  text-transform: uppercase;
  position: relative;
  top: 20px;
}

.supporting p {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  padding: 0 20px;
  margin-bottom: 20px;
  position: relative;
  top: 60px;
  text-align: center;
}

.supporting a {
  background-color: white;
  color: #333333;
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 1.3px;
  text-decoration: none;
  text-transform: uppercase;
  padding: 10px;
  margin-bottom: 10px;
  border: 2px solid #333333;
  display: inline-block;
  width: 100px;
  text-align: center;
  position: relative;
  top: 100px;
}

@media (max-width: 500px) {
  main h1 {
    font-size: 50px;
    padding: 0 40px;
  }

  .supporting .col {
    display: inline-block;
    text-align: center;
    width: 150px;
    position: relative;
    top: 140px;
  }
}
