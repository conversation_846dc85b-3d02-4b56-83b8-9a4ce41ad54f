body {
  background-color: #242f44;
  color: white;
    font-family: '<PERSON>', sans-serif;
}

header {
  text-align: center;
  margin-top: 5px;
}

h1 {
  font-size: 36px;
  padding: 15px;
  color: #8c6b48;
  background-color: white;
}

h2 {
  font-size: 24px;
  padding: 15px;
  text-transform: uppercase;
}

h3 {
  font-size: 20px;
  padding: 15px;
  text-transform: uppercase;
  text-align: left;
  margin-left: 20px;
  font-weight: 500;
  line-height: 2.7;
  letter-spacing: 0.8px;
}

th {
  border: 2px solid #8c6b48;
}

table {
  text-align: center;
  margin: 20px auto;
}

td {
  border: 2px solid #8c6b48;
  width: 300px;
}

footer {
  margin-top: 50px;
  text-align: center;
  position: fixed;
  width: 100%;
  bottom: 5px;
  background-color: #242f44;
  z-index: 5;
}
footer h3 {
  display: inline-block;
  font-size: 14px;
  background-color: #242f44;
}

.container {
  max-width: 940px;
  margin: 0 auto;
  height: 800px;
}


.left {
  width: 150px;
}
