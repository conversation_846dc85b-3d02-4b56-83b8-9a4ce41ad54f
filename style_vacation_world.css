* {
  border: 1px solid red;
}

p {
  color: green;
}

h1 {
  color: maroon;
}

.title {
  color: teal;
}

.uppercase {
  text-transform: uppercase;
}

#article-title {
  font-family: cursive;
}

a[href*='florence'] {
  color: lightgreen;
}

a[href*='beijing'] {
  color: lightblue;
}

a[href*='seoul'] {
  color: lightpink;
}

a:hover {
  color:darkorange;
}

.heading-background {
  background-color: aqua;
}

#publish-time {
  color: lightgray;
}

h5 {
  color: yellow;
}

.author-class {
  color: pink;
}

#author-id {
  color: cornflowerblue;
}

h2.destination {
  font-family: Tahoma;
}

.description h5 {
  color: blueviolet;
}

li h4 {
  color: gold;
}

h4 {
  color: dodgerblue;
}

li,
h5 {
  font-family: monospace;
}

