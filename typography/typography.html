<!DOCTYPE html>
<html>
<head>
  <title>Typography Blog</title>
  <!-- <link rel='stylesheet' href='styles/reset.css'> -->
  <link rel='stylesheet' href='typography_style.css'>
  <link href="https://fonts.googleapis.com/css2?family=Space+Mono&display=swap" rel="stylesheet">
</head>
<body>

  <!-- Header -->
  <nav class='header'>
    <ul>
      <li><a class='home' href='#top'>FAVORITE FONTS</a></li>
      <li><a class='pagelink' href='#serif'>SERIF</a></li>
      <li><a class='pagelink' href='#sans'>SANS-SERIF</a></li>
      <li><a class='pagelink' href='#mono'>MONOSPACED</a></li>
    </ul>
  </nav>

  <!-- Banner Section -->
  <div class='banner'>
    <h1>Typography</h1>
      <p>While typography has been practiced for many centuries, digital font design is a relatively new discipline. There are some great examples of old-school fonts (also called typefaces) which have been adapted for use on digital displays. However, I would like to highlight a few of my favorite fonts that were created with screens in mind.</p>
  </div>

  <!-- Serif Section -->
  <div id='serif'>

    <!-- Editorial Section - Serif -->
    <div class='editorial'>
      <h2>Serif</h2>
      <p>Serifs are the small features at the end of strokes within letters. These features are <strong>functional as well as decorative</strong>. Serif fonts are widely used for body text (eg. articles) because they are considered easier to read than sans-serif fonts in print.</p>
      <p>Serif fonts can often create a feeling of traditionalism and antiquity, due to their common use in printed materials for many years.</p>
    </div>

    <!-- Font Card - Serif -->
    <div class='font-card garamond'>
      <h2>Garamond</h2>
      <h5 class='creator'>by Claude Garamond (16th Century)</h5>
        <span class='sample'>
          <h2>Bold</h2>
          <span class='bold text'>Abc</span>
        </span>
        <span class='sample'>
          <h2>Regular</h2>
          <span class='regular text'>Abc</span>
        </span>
        <span class='sample'>
          <h2>Italic</h2>
          <span class='italic text'>Abc</span>
        </span>
    </div>
  </div>

  <!-- Sans-Serif Section -->
  <div id='sans'>

    <!-- Editorial Section - Sans-Serif -->
    <div class='editorial'>
      <h2>Sans-Serif</h2>
      <p>Sans-Serif ('without serif') describes fonts with characters which lack flourishes at the ends of the strokes. Sans-serif fonts have become the most prevalent for display of text on computer screens, as on lower-resolution digital displays, fine details like serifs may disappear or appear too large.</p>
      <p>Sans-serif fonts are often used to project an image of simplicity, modernity or minimalism.</p>
    </div>

    <!-- Font Card - Sans-Serif -->
    <div class='font-card helvetica'>
      <h2>Helvetica</h2>
      <h5 class='creator'>by Max Miedinger & Eduard Hoffman (1957)</h5>
        <span class='sample'>
          <h2>Bold</h2>
          <span class='bold text'>Abc</span>
        </span>
        <span class='sample'>
          <h2>Regular</h2>
          <span class='regular text'>Abc</span>
        </span>
        <span class='sample'>
          <h2>Italic</h2>
          <span class='italic text'>Abc</span>
        </span>
    </div>
  </div>

  <!-- Monospaced Section -->
  <div id='mono'>

    <!-- Editorial - Monospaced -->
    <div class='editorial'>
      <h2>Monospaced</h2>
      <p>A monospaced font's characters each occupy the same amount of horizontal space. This contrasts with most fonts, where the letters and spacings have different widths. For example, the two high-use letters 'I' and 'E' do not need the same footprint. The first monospaced Western typefaces were designed for typewriters.</p>
      <p>Many text editors (like those used to write computer code) use monospaced fonts, which aid in distinguishing between potentially similar characters (like 'l' and '1') and in counting the number of characters in a line.</p>
    </div>

    <!-- Font Card - Monospaced -->
    <div class='font-card space'>
      <h2>Space Mono</h2>
      <h5 class='creator'>by Colophon Foundry (2016)</h5>
        <span class='sample'>
          <h2>Regular</h2>
          <span class='regular text'>Abc</span>
        </span>
    </div>
  </div>
</body>
</html>
