@font-face {
  font-family: 'GlegooBanner';
  src: url('../fonts/Glegoo-Regular.woff2') format('woff2'),
       url('../fonts/Glegoo-Regular.woff') format('woff'),
       url('../fonts/Glegoo-Regular.ttf') format('truetype')
}

/* Universal Styles */
html {
  font-size: 16px;
  font-family: 'Arial', sans-serif;
}

body {
  background-color: #F2F2F2;  
  text-align: center;  
}

h1 {
  padding: 20px;
  color: white;
  font-size: 28px;
  text-align: center;
  font-family: Georgia;
  text-transform: uppercase;
  letter-spacing: 0.3em;
}

h2 {
  padding: 40px 20px 0 20px;
  font-size: 24px;
  text-align: center;
}

a {
  text-decoration: none;
}

p {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  text-align: justify;
}


/* Header */
.header {
  position: fixed;
  top: 0;
  width: 100%;
  padding: 20px 0;
  background-color: #fff;
  font-size: 14px;
  font-weight: 900;
}

.header li {
  display: inline-block;
  padding: 10px;
}

.header a {
  color: #4A4A4A;
}

a.home {
  color: #4D00FF;
}


/* Banner Section */
.banner {
  margin-top: 74px;
  padding: 40px 0 100px 0;
  background-color: #4D00FF;
}

.banner p {
  border-top: 1px solid #fff;
  border-bottom: 1px solid #fff;
  color: #ffffff;
  font-weight: lighter;
  word-spacing: 0.25em;
  line-height: 1.4;
  font-family: 'GlegooBanner';
  font-size: 20px;
}


/* Editorial Sections */
.editorial {
  padding-bottom: 40px;
  color: #717171;
  font-family: 'Trebuchet MS', 'Times New Roman', serif;
}

/* Font Card Sections */
.font-card {
  padding: 10px 0 40px 0;
  background-color: #ffffff;
  text-align: center;
}

.font-card .creator {
  padding: 10px 20px;
  font-size: 16px;
  font-style: italic;
}

.sample {
  display: inline-block;  
  padding: 5px 40px;
}

.sample .text {
  color: #4D00FF;
  font-size: 100px;
}

.garamond {
  font-family: Garamond;
}

.helvetica {
  font-family: Helvetica;
}

.space {
  font-family: 'Space Mono', monospace;
}

.bold {
  font-weight: 900;
}

.regular {
  font-weight: normal;
}

.italic {
  font-weight: normal;
  font-style: italic;
}
